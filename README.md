# ScrapeNinja Asynchronous Web Scraper

A powerful asynchronous web scraper that uses ScrapeNinja API for web scraping and Google Gemini AI for content parsing. This tool is designed to process business data from JSON files, extract website URLs, crawl those websites intelligently, and parse the content into structured data.

## Features

- **Asynchronous Crawling**: Fast, concurrent web scraping using aiohttp
- **Intelligent Link Discovery**: BFS-style website exploration with smart filtering
- **ScrapeNinja Integration**: Uses ScrapeNinja API for reliable web scraping with markdown extraction
- **Gemini AI Parsing**: Leverages Google Gemini AI to parse and structure scraped content
- **Rate Limiting**: Respectful crawling with configurable delays
- **Robust Error Handling**: Comprehensive retry logic and error recovery
- **Flexible Configuration**: Customizable crawl depth, page limits, and delays
- **Multiple Input Support**: Process multiple JSON files with business data

## Architecture

The scraper works in several phases:

1. **Input Processing**: Reads JSON files containing business data and extracts website URLs
2. **Website Crawling**: For each website:
   - Starts from the homepage
   - Discovers internal links using BFS algorithm
   - Filters out non-content URLs (media files, admin pages, etc.)
   - Respects crawl depth and page limits
   - Uses ScrapeNinja API for reliable content extraction
3. **Content Combination**: Merges all scraped pages into a single markdown document
4. **AI Parsing**: Uses Gemini AI to extract structured data from the combined content
5. **Output Generation**: Saves results in JSON format with metadata

## Installation

1. Clone or download the project files
2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up your API keys in the `.env` file:
```
GEMINI_API_KEY=your_gemini_api_key_here
SCRAPENINJA_API_KEY=your_scrapeninja_api_key_here
```

## Usage

### Basic Usage

Process all JSON files in the `input` directory:
```bash
python run_scraper.py
```

### Advanced Usage

```bash
# Custom configuration
python run_scraper.py --max-pages 10 --max-depth 3 --delay 2.0

# Process a specific file
python run_scraper.py --single-file "input/Wichita KS.json"

# Test with a single URL
python run_scraper.py --single-url "https://example-vet.com"

# Custom directories
python run_scraper.py --input-dir "my_input" --output-dir "my_output"
```

### Command Line Options

- `--input-dir DIR`: Directory containing input JSON files (default: input)
- `--output-dir DIR`: Directory for output files (default: output)
- `--scraped-data-dir DIR`: Directory for scraped data (default: scraped_data)
- `--max-pages N`: Maximum pages to scrape per website (default: 20)
- `--max-depth N`: Maximum crawl depth (default: 2)
- `--delay SECONDS`: Delay between requests (default: 1.5)
- `--single-file FILE`: Process only a specific input file
- `--single-url URL`: Scrape and parse a single URL (for testing)

## Input Format

The scraper expects JSON files containing business data. Each business object should have a `website` field with a valid URL. Example:

```json
[
  {
    "name": "Example Veterinary Clinic",
    "website": "https://example-vet.com",
    "formatted_address": "123 Main St, City, State 12345",
    "rating": 4.5,
    "user_ratings_total": 150,
    "business_status": "OPERATIONAL"
  }
]
```

## Output Structure

The scraper generates several types of output files:

### 1. Raw Scraped Data
- Location: `scraped_data/`
- Format: JSON files with complete crawl results
- Contains: URLs visited, content extracted, timestamps, metadata

### 2. Combined Content
- Location: `scraped_data/`
- Format: Markdown files
- Contains: All scraped content combined into a single document

### 3. Parsed Results
- Location: `output/`
- Format: JSON files with structured data
- Contains: AI-parsed business information

### 4. Summary Reports
- Location: `output/all_results_summary.json`
- Contains: Overview of all processing results

## Configuration

### Crawl Configuration

The `CrawlConfig` class allows you to customize crawling behavior:

```python
config = CrawlConfig(
    max_pages=20,           # Maximum pages per website
    max_depth=2,            # Maximum link depth from homepage
    delay_between_requests=1.5,  # Seconds between requests
    timeout=30,             # Request timeout in seconds
    retries=3,              # Number of retry attempts
    geo="us",               # Geographic location for ScrapeNinja
    extractor_preset="markdown"  # Content extraction format
)
```

### URL Filtering

The scraper automatically filters URLs to focus on content pages:

- **Included**: Internal links, content pages, navigation pages
- **Excluded**: Media files, admin pages, external links, tracking URLs

## API Integration

### ScrapeNinja API

The scraper uses ScrapeNinja's API for reliable web scraping:
- Handles JavaScript rendering
- Provides markdown content extraction
- Includes geographic targeting
- Offers retry mechanisms

### Gemini AI API

Content parsing is handled by Google's Gemini AI:
- Extracts structured business information
- Identifies contact details, services, hours
- Provides intelligent content summarization
- Handles various website formats

## Error Handling

The scraper includes comprehensive error handling:

- **Network Errors**: Automatic retries with exponential backoff
- **API Failures**: Graceful degradation and error logging
- **Invalid URLs**: URL validation and normalization
- **Content Issues**: Partial data extraction when possible

## Performance Considerations

- **Asynchronous Processing**: Multiple websites can be processed concurrently
- **Rate Limiting**: Configurable delays prevent overwhelming target servers
- **Memory Management**: Streaming processing for large datasets
- **API Quotas**: Built-in respect for API rate limits

## Troubleshooting

### Common Issues

1. **API Key Errors**: Ensure your API keys are correctly set in the `.env` file
2. **Network Timeouts**: Increase the timeout value for slow websites
3. **Rate Limiting**: Increase delay between requests if getting blocked
4. **Memory Issues**: Reduce max_pages for large websites

### Debugging

Use the single URL mode for testing:
```bash
python run_scraper.py --single-url "https://example.com"
```

Check the logs for detailed error information and processing status.

## File Structure

```
├── async_scraper.py          # Core asynchronous scraping logic
├── scraper_orchestrator.py   # Main orchestration and coordination
├── run_scraper.py           # CLI interface
├── gemini_parser.py         # Existing Gemini AI integration
├── gemini_api.py           # Existing Gemini API utilities
├── requirements.txt         # Python dependencies
├── .env                    # API keys (create this file)
├── input/                  # Input JSON files
├── output/                 # Parsed results
└── scraped_data/          # Raw scraped content
```

## License

This project is provided as-is for educational and commercial use.

## Support

For issues or questions, please check the logs for detailed error information. The scraper provides comprehensive logging to help diagnose any problems.
