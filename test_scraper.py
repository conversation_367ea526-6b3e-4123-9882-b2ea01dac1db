#!/usr/bin/env python3
"""
Test script for the ScrapeNinja Asynchronous Web Scraper
"""

import asyncio
import os
import json
from dotenv import load_dotenv

from async_scraper import AsyncWebCrawler, CrawlConfig
from gemini_parser import GeminiParser

# Load environment variables
load_dotenv()

async def test_single_url_scraping():
    """Test scraping a single URL"""
    print("Testing single URL scraping...")
    
    # Test with a simple website
    test_url = "https://httpbin.org/html"  # Simple test page
    
    config = CrawlConfig(
        max_pages=3,
        max_depth=1,
        delay_between_requests=1.0
    )
    
    try:
        crawler = AsyncWebCrawler(config)
        results = await crawler.crawl_website(test_url, "test_output")
        
        print(f"✓ Successfully scraped {len(results['scraped_content'])} pages")
        print(f"✓ Visited URLs: {len(results['visited_urls'])}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during scraping: {e}")
        return False

def test_environment_setup():
    """Test that environment variables are set up correctly"""
    print("Testing environment setup...")
    
    scrapeninja_key = os.getenv('SCRAPENINJA_API_KEY')
    gemini_key = os.getenv('GEMINI_API_KEY')
    
    if not scrapeninja_key:
        print("✗ SCRAPENINJA_API_KEY not found in environment")
        return False
    else:
        print("✓ SCRAPENINJA_API_KEY found")
    
    if not gemini_key:
        print("✗ GEMINI_API_KEY not found in environment")
        return False
    else:
        print("✓ GEMINI_API_KEY found")
    
    return True

def test_input_file_reading():
    """Test reading the sample input file"""
    print("Testing input file reading...")
    
    input_file = "input/Wichita KS.json"
    
    if not os.path.exists(input_file):
        print(f"✗ Input file {input_file} not found")
        return False
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✓ Successfully loaded {len(data)} businesses from input file")
        
        # Count businesses with websites
        websites = [b for b in data if isinstance(b, dict) and b.get('website')]
        print(f"✓ Found {len(websites)} businesses with websites")
        
        if websites:
            print(f"✓ Example website: {websites[0]['website']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error reading input file: {e}")
        return False

async def test_gemini_parsing():
    """Test Gemini AI parsing with sample content"""
    print("Testing Gemini AI parsing...")
    
    gemini_key = os.getenv('GEMINI_API_KEY')
    if not gemini_key:
        print("✗ Cannot test Gemini parsing - API key not found")
        return False
    
    try:
        parser = GeminiParser(gemini_key)
        
        # Sample content to parse
        sample_content = """
        # Website Content for Test Veterinary Clinic
        
        ## About Us
        Welcome to Test Veterinary Clinic! We provide comprehensive veterinary care for dogs and cats.
        
        ## Contact Information
        Address: 123 Main Street, Test City, TC 12345
        Phone: (*************
        Email: <EMAIL>
        
        ## Services
        - Routine checkups
        - Vaccinations
        - Surgery
        - Emergency care
        
        ## Hours
        Monday-Friday: 8:00 AM - 6:00 PM
        Saturday: 9:00 AM - 4:00 PM
        Sunday: Closed
        """
        
        result = parser.parse_content(sample_content)
        
        if result and isinstance(result, dict):
            print("✓ Gemini parsing successful")
            print(f"✓ Extracted business name: {result.get('name', 'Not found')}")
            print(f"✓ Found {len(result.get('services', []))} services")
            return True
        else:
            print("✗ Gemini parsing failed - no valid result")
            return False
            
    except Exception as e:
        print(f"✗ Error during Gemini parsing: {e}")
        return False

def test_directory_structure():
    """Test that required directories exist or can be created"""
    print("Testing directory structure...")
    
    required_dirs = ['input', 'output', 'scraped_data']
    
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            try:
                os.makedirs(dir_name, exist_ok=True)
                print(f"✓ Created directory: {dir_name}")
            except Exception as e:
                print(f"✗ Failed to create directory {dir_name}: {e}")
                return False
        else:
            print(f"✓ Directory exists: {dir_name}")
    
    return True

async def run_all_tests():
    """Run all tests"""
    print("=" * 60)
    print("ScrapeNinja Asynchronous Web Scraper - Test Suite")
    print("=" * 60)
    print()
    
    tests = [
        ("Environment Setup", test_environment_setup),
        ("Directory Structure", test_directory_structure),
        ("Input File Reading", test_input_file_reading),
        ("Gemini AI Parsing", test_gemini_parsing),
        ("Single URL Scraping", test_single_url_scraping),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"Running {test_name}...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
        print()
    
    # Summary
    print("=" * 60)
    print("Test Results Summary")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:.<40} {status}")
    
    print()
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The scraper is ready to use.")
        print("\nTo get started, run:")
        print("  python run_scraper.py --single-url 'https://example.com'")
        print("  python run_scraper.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\nCommon fixes:")
        print("- Make sure API keys are set in .env file")
        print("- Check internet connection")
        print("- Verify input files exist")

if __name__ == "__main__":
    asyncio.run(run_all_tests())
