#!/usr/bin/env python3
"""
ScrapeNinja Asynchronous Web Scraper
====================================

This script processes JSON files containing business data, extracts website URLs,
crawls those websites using ScrapeNinja API, and parses the content using Gemini AI.

Usage:
    python run_scraper.py [options]

Options:
    --input-dir DIR         Directory containing input JSON files (default: input)
    --output-dir DIR        Directory for output files (default: output)
    --scraped-data-dir DIR  Directory for scraped data (default: scraped_data)
    --max-pages N           Maximum pages to scrape per website (default: 20)
    --max-depth N           Maximum crawl depth (default: 2)
    --delay SECONDS         Delay between requests (default: 0)
    --single-file FILE      Process only a specific input file
    --single-url URL        Scrape and parse a single URL (for testing)
    --help                  Show this help message

Examples:
    # Process all files in input directory
    python run_scraper.py

    # Process with custom settings
    python run_scraper.py --max-pages 10 --delay 2.0

    # Process only one file
    python run_scraper.py --single-file "input/Wichita KS.json"

    # Test with a single URL
    python run_scraper.py --single-url "https://example-vet.com"
"""

import asyncio
import argparse
import sys
import os
import json
from pathlib import Path
from datetime import datetime

from async_scraper import AsyncWebCrawler, CrawlConfig
from scraper_orchestrator import ScraperOrchestrator
from gemini_parser import GeminiParser

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="ScrapeNinja Asynchronous Web Scraper",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )

    parser.add_argument(
        '--input-dir',
        default='input',
        help='Directory containing input JSON files (default: input)'
    )

    parser.add_argument(
        '--output-dir',
        default='output',
        help='Directory for output files (default: output)'
    )

    parser.add_argument(
        '--scraped-data-dir',
        default='scraped_data',
        help='Directory for scraped data (default: scraped_data)'
    )

    parser.add_argument(
        '--max-pages',
        type=int,
        default=20,
        help='Maximum pages to scrape per website (default: 20)'
    )

    parser.add_argument(
        '--max-depth',
        type=int,
        default=2,
        help='Maximum crawl depth (default: 2)'
    )

    parser.add_argument(
        '--delay',
        type=float,
        default=0,
        help='Delay between requests in seconds (default: 0)'
    )

    parser.add_argument(
        '--single-file',
        help='Process only a specific input file'
    )

    parser.add_argument(
        '--single-url',
        help='Scrape and parse a single URL (for testing)'
    )

    parser.add_argument(
        '--max-concurrent',
        type=int,
        default=3,
        help='Maximum concurrent businesses to process (default: 3)'
    )

    return parser.parse_args()

async def process_single_url(url: str, config: CrawlConfig):
    """Process a single URL for testing purposes"""
    print(f"Testing with single URL: {url}")

    try:
        # Scrape the website
        crawler = AsyncWebCrawler(config)
        crawl_results = await crawler.crawl_website(url, "test_scraped_data")

        print(f"Scraped {len(crawl_results['scraped_content'])} pages")

        # Combine content
        combined_content = []
        combined_content.append(f"# Test Crawl Results")
        combined_content.append(f"# URL: {url}")
        combined_content.append("")

        for i, page_data in enumerate(crawl_results['scraped_content'], 1):
            page_url = page_data.get('url', 'Unknown URL')
            content = page_data.get('content', '')

            combined_content.append("=" * 80)
            combined_content.append(f"Page {i}: {page_url}")
            combined_content.append("=" * 80)
            combined_content.append("")
            combined_content.append(content)
            combined_content.append("")

        combined_text = "\n".join(combined_content)

        # Save combined content
        os.makedirs("test_output", exist_ok=True)
        with open("test_output/test_combined.md", 'w', encoding='utf-8') as f:
            f.write(combined_text)

        print("Combined content saved to test_output/test_combined.md")

        # Parse with Gemini
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if gemini_api_key:
            print("Parsing with Gemini...")
            parser = GeminiParser(gemini_api_key)
            parsed_data = parser.parse_content(combined_text, homepage_url=url)

            # Save parsed results
            import json
            with open("test_output/test_parsed.json", 'w', encoding='utf-8') as f:
                json.dump(parsed_data, f, indent=2, ensure_ascii=False)

            print("Parsed results saved to test_output/test_parsed.json")
        else:
            print("GEMINI_API_KEY not found, skipping parsing")

    except Exception as e:
        print(f"Error processing URL: {e}")

async def main():
    """Main function"""
    args = parse_arguments()

    # Check environment variables
    if not os.getenv('SCRAPENINJA_API_KEY'):
        print("Error: SCRAPENINJA_API_KEY not found in environment variables")
        print("Please add it to your .env file")
        sys.exit(1)

    if not os.getenv('GEMINI_API_KEY'):
        print("Warning: GEMINI_API_KEY not found in environment variables")
        print("Scraping will work but parsing will be skipped")

    # Create crawl configuration
    config = CrawlConfig(
        max_pages=args.max_pages,
        max_depth=args.max_depth,
        delay_between_requests=args.delay,
        timeout=30,
        retries=3
    )

    print(f"Crawl Configuration:")
    print(f"  Max pages per site: {config.max_pages}")
    print(f"  Max depth: {config.max_depth}")
    print(f"  Delay between requests: {config.delay_between_requests}s")
    print(f"  Max concurrent businesses: {args.max_concurrent}")
    print()

    # Handle single URL testing
    if args.single_url:
        await process_single_url(args.single_url, config)
        return

    # Create orchestrator
    orchestrator = ScraperOrchestrator(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        scraped_data_dir=args.scraped_data_dir,
        crawl_config=config
    )

    # Process files
    if args.single_file:
        if not os.path.exists(args.single_file):
            print(f"Error: File {args.single_file} not found")
            sys.exit(1)

        print(f"Processing single file: {args.single_file} (max {args.max_concurrent} concurrent)")
        results = await orchestrator.process_input_file(args.single_file, args.max_concurrent)
        print(f"Processed {len(results)} businesses from {args.single_file}")

    else:
        print(f"Processing all files in {args.input_dir} (max {args.max_concurrent} concurrent per file)")

        # Update process_all_files to accept max_concurrent parameter
        input_files = orchestrator.find_input_files()
        if not input_files:
            print(f"No JSON files found in {args.input_dir}")
            return

        all_results = {}

        for input_file in input_files:
            try:
                results = await orchestrator.process_input_file(input_file, args.max_concurrent)
                filename = Path(input_file).stem
                all_results[filename] = results

            except Exception as e:
                print(f"Error processing {input_file}: {e}")
                all_results[Path(input_file).stem] = []

        # Save summary results
        summary_file = os.path.join(args.output_dir, "all_results_summary.json")
        summary = {
            'timestamp': datetime.now().isoformat(),
            'total_files_processed': len(input_files),
            'total_businesses_processed': sum(len(results) for results in all_results.values()),
            'max_concurrent': args.max_concurrent,
            'files': all_results
        }

        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        total_businesses = sum(len(file_results) for file_results in all_results.values())
        print(f"\nProcessing complete!")
        print(f"Total files processed: {len(all_results)}")
        print(f"Total businesses processed: {total_businesses}")
        print(f"Summary saved to {summary_file}")

        for filename, file_results in all_results.items():
            print(f"  {filename}: {len(file_results)} businesses")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)
