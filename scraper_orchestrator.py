import asyncio
import json
import os
import glob
from typing import List, Dict, Optional
import logging
from datetime import datetime
from pathlib import Path

from async_scraper import AsyncWebCrawler, CrawlConfig
from gemini_parser import GeminiParser

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ScraperOrchestrator:
    """Orchestrates the entire scraping and parsing process"""
    
    def __init__(self, 
                 input_dir: str = "input",
                 output_dir: str = "output",
                 scraped_data_dir: str = "scraped_data",
                 crawl_config: CrawlConfig = None):
        
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.scraped_data_dir = scraped_data_dir
        self.crawl_config = crawl_config or CrawlConfig()
        
        # Initialize Gemini parser
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if not gemini_api_key:
            raise ValueError("GEMINI_API_KEY not found in environment variables")
        
        self.gemini_parser = GeminiParser(gemini_api_key)
        
        # Create output directories
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.scraped_data_dir, exist_ok=True)
    
    def extract_websites_from_json(self, json_file_path: str) -> List[Dict]:
        """Extract website URLs from business data JSON file"""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                businesses = json.load(f)
            
            websites = []
            for business in businesses:
                if isinstance(business, dict) and 'website' in business:
                    website_url = business['website']
                    if website_url and website_url.startswith(('http://', 'https://')):
                        websites.append({
                            'url': website_url,
                            'business_name': business.get('name', 'Unknown'),
                            'business_data': business
                        })
                        logger.info(f"Found website: {website_url} for {business.get('name', 'Unknown')}")
            
            logger.info(f"Extracted {len(websites)} websites from {json_file_path}")
            return websites
            
        except Exception as e:
            logger.error(f"Error reading {json_file_path}: {e}")
            return []
    
    def find_input_files(self) -> List[str]:
        """Find all JSON files in the input directory"""
        pattern = os.path.join(self.input_dir, "*.json")
        files = glob.glob(pattern)
        logger.info(f"Found {len(files)} JSON files in {self.input_dir}")
        return files
    
    async def scrape_website(self, website_info: Dict) -> Optional[Dict]:
        """Scrape a single website"""
        try:
            crawler = AsyncWebCrawler(self.crawl_config)
            
            logger.info(f"Starting to crawl {website_info['url']} for {website_info['business_name']}")
            
            crawl_results = await crawler.crawl_website(
                website_info['url'], 
                self.scraped_data_dir
            )
            
            # Add business context to results
            crawl_results['business_info'] = website_info['business_data']
            
            return crawl_results
            
        except Exception as e:
            logger.error(f"Error crawling {website_info['url']}: {e}")
            return None
    
    def combine_scraped_content(self, crawl_results: Dict) -> str:
        """Combine all scraped content into a single markdown document"""
        combined_content = []
        
        # Add header with business info
        business_info = crawl_results.get('business_info', {})
        business_name = business_info.get('name', 'Unknown Business')
        start_url = crawl_results.get('start_url', 'Unknown URL')
        
        combined_content.append(f"# Website Crawl Results for {business_name}")
        combined_content.append(f"# Starting URL: {start_url}")
        combined_content.append(f"# Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        combined_content.append("")
        
        # Add business context if available
        if business_info:
            combined_content.append("## Business Information")
            if 'formatted_address' in business_info:
                combined_content.append(f"**Address:** {business_info['formatted_address']}")
            if 'rating' in business_info:
                combined_content.append(f"**Rating:** {business_info['rating']}")
            if 'user_ratings_total' in business_info:
                combined_content.append(f"**Total Reviews:** {business_info['user_ratings_total']}")
            combined_content.append("")
        
        # Add scraped content
        scraped_content = crawl_results.get('scraped_content', [])
        
        for i, page_data in enumerate(scraped_content, 1):
            url = page_data.get('url', 'Unknown URL')
            content = page_data.get('content', '')
            
            combined_content.append("=" * 80)
            combined_content.append(f"Page {i}: {url}")
            combined_content.append("=" * 80)
            combined_content.append("")
            combined_content.append(content)
            combined_content.append("")
        
        return "\n".join(combined_content)
    
    def save_combined_content(self, combined_content: str, business_name: str, timestamp: int) -> str:
        """Save combined content to a file"""
        # Clean business name for filename
        safe_name = "".join(c for c in business_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_name = safe_name.replace(' ', '_')
        
        filename = f"{safe_name}_{timestamp}_combined.md"
        filepath = os.path.join(self.scraped_data_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(combined_content)
        
        logger.info(f"Combined content saved to {filepath}")
        return filepath
    
    async def process_single_business(self, website_info: Dict) -> Optional[Dict]:
        """Process a single business: scrape and parse"""
        try:
            # Scrape the website
            crawl_results = await self.scrape_website(website_info)
            if not crawl_results:
                return None
            
            # Combine scraped content
            combined_content = self.combine_scraped_content(crawl_results)
            
            # Save combined content
            timestamp = crawl_results.get('timestamp', int(datetime.now().timestamp()))
            business_name = website_info['business_name']
            
            combined_file = self.save_combined_content(combined_content, business_name, timestamp)
            
            # Set location info for Gemini parser
            self.gemini_parser.set_location_info(website_info['business_data'])
            
            # Parse with Gemini
            logger.info(f"Parsing content with Gemini for {business_name}")
            parsed_data = self.gemini_parser.parse_content(
                combined_content,
                save_preprocessed=True,
                homepage_url=website_info['url']
            )
            
            # Add metadata
            parsed_data['scraping_metadata'] = {
                'start_url': website_info['url'],
                'business_name': business_name,
                'pages_scraped': crawl_results.get('config', {}).get('pages_scraped', 0),
                'timestamp': timestamp,
                'combined_content_file': combined_file
            }
            
            return parsed_data
            
        except Exception as e:
            logger.error(f"Error processing business {website_info['business_name']}: {e}")
            return None
    
    async def process_input_file(self, input_file: str) -> List[Dict]:
        """Process a single input JSON file"""
        logger.info(f"Processing input file: {input_file}")
        
        # Extract websites from the file
        websites = self.extract_websites_from_json(input_file)
        if not websites:
            logger.warning(f"No websites found in {input_file}")
            return []
        
        # Process each website
        results = []
        for website_info in websites:
            result = await self.process_single_business(website_info)
            if result:
                results.append(result)
            
            # Add delay between businesses to be respectful
            await asyncio.sleep(2)
        
        # Save results for this input file
        input_filename = Path(input_file).stem
        output_file = os.path.join(self.output_dir, f"{input_filename}_parsed_results.json")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Results for {input_file} saved to {output_file}")
        return results
    
    async def process_all_files(self) -> Dict:
        """Process all input files"""
        input_files = self.find_input_files()
        if not input_files:
            logger.warning(f"No JSON files found in {self.input_dir}")
            return {}
        
        all_results = {}
        
        for input_file in input_files:
            try:
                results = await self.process_input_file(input_file)
                filename = Path(input_file).stem
                all_results[filename] = results
                
            except Exception as e:
                logger.error(f"Error processing {input_file}: {e}")
                all_results[Path(input_file).stem] = []
        
        # Save summary results
        summary_file = os.path.join(self.output_dir, "all_results_summary.json")
        summary = {
            'timestamp': datetime.now().isoformat(),
            'total_files_processed': len(input_files),
            'total_businesses_processed': sum(len(results) for results in all_results.values()),
            'files': all_results
        }
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Processing complete. Summary saved to {summary_file}")
        return all_results

async def main():
    """Main function to run the scraper orchestrator"""
    
    # Configure crawling parameters
    config = CrawlConfig(
        max_pages=20,      # Limit pages per website
        max_depth=2,       # Don't go too deep
        delay_between_requests=1.5,  # Be respectful
        timeout=30,
        retries=3
    )
    
    # Create orchestrator
    orchestrator = ScraperOrchestrator(crawl_config=config)
    
    # Process all files
    results = await orchestrator.process_all_files()
    
    print(f"\nProcessing complete!")
    print(f"Total files processed: {len(results)}")
    for filename, file_results in results.items():
        print(f"  {filename}: {len(file_results)} businesses processed")

if __name__ == "__main__":
    asyncio.run(main())
