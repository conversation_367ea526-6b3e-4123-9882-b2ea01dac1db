#!/usr/bin/env python3
"""
Test script to validate ScrapeNinja API integration and debug 422 errors
"""

import asyncio
import aiohttp
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_scrapeninja_api():
    """Test ScrapeNinja API with various URLs to identify 422 error causes"""
    
    api_key = os.getenv('SCRAPENINJA_API_KEY')
    if not api_key:
        print("❌ SCRAPENINJA_API_KEY not found in environment variables")
        return
    
    print("🧪 Testing ScrapeNinja API Integration")
    print("=" * 50)
    
    # Test URLs - from simple to complex
    test_urls = [
        "https://httpbin.org/html",  # Simple test page
        "https://example.com",       # Basic website
        "https://allcreaturespetvet.com/",  # From your data
        "https://exoticpetswichita.com/",   # From your data
    ]
    
    base_url = "https://scrapeninja.apiroad.net/scrape"
    headers = {
        "Content-Type": "application/json",
        "x-apiroad-key": api_key
    }
    
    async with aiohttp.ClientSession() as session:
        for i, test_url in enumerate(test_urls, 1):
            print(f"\n🔍 Test {i}: {test_url}")
            
            # Test with minimal payload first
            minimal_payload = {
                "url": test_url,
                "method": "GET"
            }
            
            print(f"   📤 Testing minimal payload: {minimal_payload}")
            
            try:
                async with session.post(
                    base_url,
                    json=minimal_payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    print(f"   📥 Response Status: {response.status}")
                    
                    if response.status == 200:
                        result = await response.json()
                        print(f"   ✅ Success! Content length: {len(str(result))}")
                        
                        # Check if extractor data is present
                        if 'extractor' in result:
                            print(f"   📄 Extractor data available")
                            if 'result' in result['extractor']:
                                extractor_result = result['extractor']['result']
                                if 'markdown' in extractor_result:
                                    content_length = len(extractor_result['markdown'])
                                    print(f"   📝 Markdown content: {content_length} characters")
                        else:
                            print(f"   ⚠️  No extractor data in response")
                            
                    elif response.status == 422:
                        error_text = await response.text()
                        print(f"   ❌ HTTP 422 Validation Error")
                        print(f"   📄 Response: {error_text}")
                        
                        try:
                            error_data = await response.json()
                            print(f"   🔍 Error details: {json.dumps(error_data, indent=2)}")
                        except:
                            print(f"   ⚠️  Could not parse error response as JSON")
                            
                    else:
                        error_text = await response.text()
                        print(f"   ❌ HTTP {response.status}: {error_text}")
                        
            except Exception as e:
                print(f"   💥 Exception: {e}")
            
            # Test with full payload
            print(f"   📤 Testing full payload...")
            
            full_payload = {
                "url": test_url,
                "method": "GET",
                "retryNum": 3,
                "geo": "us",
                "extractorPreset": "markdown"
            }
            
            try:
                async with session.post(
                    base_url,
                    json=full_payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    print(f"   📥 Full payload response: {response.status}")
                    
                    if response.status == 200:
                        result = await response.json()
                        print(f"   ✅ Full payload success!")
                        
                    elif response.status == 422:
                        error_text = await response.text()
                        print(f"   ❌ Full payload 422 error: {error_text}")
                        
                        try:
                            error_data = await response.json()
                            print(f"   🔍 Full payload error details: {json.dumps(error_data, indent=2)}")
                        except:
                            pass
                            
                    else:
                        error_text = await response.text()
                        print(f"   ❌ Full payload HTTP {response.status}: {error_text}")
                        
            except Exception as e:
                print(f"   💥 Full payload exception: {e}")
            
            # Small delay between tests
            await asyncio.sleep(1)
    
    print(f"\n🏁 API testing completed")

async def test_api_key_validation():
    """Test if the API key is valid"""
    
    api_key = os.getenv('SCRAPENINJA_API_KEY')
    if not api_key:
        print("❌ SCRAPENINJA_API_KEY not found")
        return False
    
    print(f"🔑 Testing API key: {api_key[:10]}...")
    
    base_url = "https://scrapeninja.apiroad.net/scrape"
    headers = {
        "Content-Type": "application/json",
        "x-apiroad-key": api_key
    }
    
    # Simple test payload
    payload = {
        "url": "https://httpbin.org/html",
        "method": "GET"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                base_url,
                json=payload,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                
                if response.status == 200:
                    print("✅ API key is valid")
                    return True
                elif response.status == 401:
                    print("❌ API key is invalid or expired")
                    return False
                elif response.status == 403:
                    print("❌ API key lacks permissions")
                    return False
                else:
                    error_text = await response.text()
                    print(f"⚠️  Unexpected response {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"💥 Error testing API key: {e}")
        return False

async def main():
    """Main test function"""
    
    print("🚀 ScrapeNinja API Diagnostic Tool")
    print("=" * 40)
    
    # Test 1: API key validation
    print("\n📋 Step 1: API Key Validation")
    key_valid = await test_api_key_validation()
    
    if not key_valid:
        print("\n❌ Cannot proceed - API key issues detected")
        print("Please check your SCRAPENINJA_API_KEY in the .env file")
        return
    
    # Test 2: API integration testing
    print("\n📋 Step 2: API Integration Testing")
    await test_scrapeninja_api()
    
    print("\n🎉 Diagnostic completed!")
    print("\nIf you're still seeing 422 errors, the issue might be:")
    print("1. API rate limiting or quota exceeded")
    print("2. Specific URL formatting issues")
    print("3. Temporary API service issues")
    print("4. Account-specific restrictions")

if __name__ == "__main__":
    asyncio.run(main())
