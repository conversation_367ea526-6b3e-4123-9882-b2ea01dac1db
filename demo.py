#!/usr/bin/env python3
"""
Demo script for the ScrapeNinja Asynchronous Web Scraper

This script demonstrates concurrent crawling of multiple businesses
from your input file to showcase true asynchronous processing.
"""

import asyncio
import json
import os
from datetime import datetime
import time

from async_scraper import Async<PERSON>eb<PERSON>rawler, CrawlConfig
from gemini_parser import GeminiParser

async def scrape_single_business(business_info, config, business_id):
    """Scrape a single business website asynchronously"""
    business_name = business_info.get('name', f'Business {business_id}')
    website_url = business_info['website']

    print(f"🕷️  [{business_id}] Starting crawl for {business_name}")

    try:
        crawler = AsyncWebCrawler(config)
        crawl_results = await crawler.crawl_website(
            website_url,
            f"demo_scraped_data/business_{business_id}"
        )

        pages_scraped = len(crawl_results['scraped_content'])
        print(f"✅ [{business_id}] {business_name}: {pages_scraped} pages scraped")

        return {
            'business_info': business_info,
            'crawl_data': crawl_results,
            'business_id': business_id,
            'success': True
        }

    except Exception as e:
        print(f"❌ [{business_id}] {business_name} failed: {str(e)}")
        return {
            'business_info': business_info,
            'business_id': business_id,
            'success': False,
            'error': str(e)
        }

async def process_business_with_gemini(business_result, gemini_parser):
    """Process a single business result with Gemini AI"""
    if not business_result['success']:
        return business_result

    business_info = business_result['business_info']
    crawl_data = business_result['crawl_data']
    business_id = business_result['business_id']
    business_name = business_info.get('name', f'Business {business_id}')

    print(f"🤖 [{business_id}] Parsing {business_name} with Gemini AI...")

    try:
        # Combine scraped content
        combined_content = []
        combined_content.append(f"# Website Crawl Results for {business_name}")
        combined_content.append(f"# Starting URL: {business_info['website']}")
        combined_content.append(f"# Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        combined_content.append("")

        # Add business context
        combined_content.append("## Business Information")
        if 'formatted_address' in business_info:
            combined_content.append(f"**Address:** {business_info['formatted_address']}")
        if 'rating' in business_info:
            combined_content.append(f"**Rating:** {business_info['rating']}")
        combined_content.append("")

        # Add scraped content
        for i, page_data in enumerate(crawl_data['scraped_content'], 1):
            url = page_data.get('url', 'Unknown URL')
            content = page_data.get('content', '')

            combined_content.append("=" * 80)
            combined_content.append(f"Page {i}: {url}")
            combined_content.append("=" * 80)
            combined_content.append("")
            combined_content.append(content)
            combined_content.append("")

        combined_text = "\n".join(combined_content)

        # Parse with Gemini
        gemini_parser.set_location_info(business_info)
        parsed_data = gemini_parser.parse_content(
            combined_text,
            homepage_url=business_info['website']
        )

        business_result['parsed_data'] = parsed_data
        business_result['combined_content'] = combined_text
        print(f"✅ [{business_id}] {business_name} parsing completed")

        return business_result

    except Exception as e:
        print(f"❌ [{business_id}] {business_name} parsing failed: {str(e)}")
        business_result['parsing_error'] = str(e)
        return business_result

async def demo_concurrent_crawling():
    """Demo concurrent scraping and parsing for multiple businesses"""

    print("🚀 ScrapeNinja Asynchronous Web Scraper Demo - Concurrent Mode")
    print("=" * 60)

    # Load businesses from the input file
    input_file = "input/Wichita KS.json"

    if not os.path.exists(input_file):
        print(f"❌ Input file {input_file} not found")
        return

    with open(input_file, 'r', encoding='utf-8') as f:
        businesses = json.load(f)

    # Find businesses with websites (limit to first 3 for demo)
    target_businesses = []
    for business in businesses:
        if isinstance(business, dict) and business.get('website'):
            website_url = business['website']
            if website_url.startswith(('http://', 'https://')):
                target_businesses.append(business)
                if len(target_businesses) >= 3:  # Limit for demo
                    break

    if not target_businesses:
        print("❌ No businesses with websites found in the input file")
        return

    print(f"📋 Selected {len(target_businesses)} businesses for concurrent processing:")
    for i, business in enumerate(target_businesses, 1):
        print(f"   {i}. {business.get('name', 'Unknown')} - {business['website']}")
    print()

    # Configure the crawler for concurrent demo
    config = CrawlConfig(
        max_pages=3,        # Limit to 3 pages per site for demo
        max_depth=2,        # Don't go too deep
        delay_between_requests=0.5,  # Faster for demo
        timeout=30,
        retries=2
    )

    print("⚙️  Crawl Configuration:")
    print(f"   Max pages per site: {config.max_pages}")
    print(f"   Max depth: {config.max_depth}")
    print(f"   Delay: {config.delay_between_requests}s")
    print(f"   Processing {len(target_businesses)} sites concurrently")
    print()

    try:
        # Step 1: Scrape all websites concurrently
        print("🕷️  Step 1: Scraping websites concurrently...")
        start_time = time.time()

        # Create tasks for concurrent crawling
        crawl_tasks = []
        for i, business in enumerate(target_businesses):
            task = scrape_single_business(business, config, i + 1)
            crawl_tasks.append(task)

        # Execute all crawling tasks concurrently
        print(f"🚀 Starting {len(crawl_tasks)} concurrent crawl tasks...")
        crawl_results = await asyncio.gather(*crawl_tasks, return_exceptions=True)

        crawl_end_time = time.time()
        crawl_time = crawl_end_time - start_time

        # Process crawling results
        successful_crawls = []
        failed_crawls = 0

        for result in crawl_results:
            if isinstance(result, Exception):
                print(f"❌ Crawl task failed with exception: {result}")
                failed_crawls += 1
            elif result and result.get('success'):
                successful_crawls.append(result)
            else:
                failed_crawls += 1

        print(f"\n📊 Crawling Summary:")
        print(f"   ✅ Successful crawls: {len(successful_crawls)}")
        print(f"   ❌ Failed crawls: {failed_crawls}")
        print(f"   ⏱️  Total crawl time: {crawl_time:.1f} seconds")
        if len(target_businesses) > 0:
            print(f"   🚀 Average time per site: {crawl_time/len(target_businesses):.1f} seconds")

        if not successful_crawls:
            print("❌ No websites were successfully scraped.")
            return

        # Step 2: Parse with Gemini AI concurrently (if API key available)
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if not gemini_api_key:
            print("\n❌ GEMINI_API_KEY not found. Skipping AI parsing.")
            print("   Add your Gemini API key to the .env file to enable parsing.")
        else:
            print(f"\n🤖 Step 2: Parsing {len(successful_crawls)} businesses with Gemini AI concurrently...")

            # Note: Gemini parsing is done sequentially to avoid rate limits
            # In production, you might want to implement semaphores for controlled concurrency
            parser = GeminiParser(gemini_api_key)

            parsing_start_time = time.time()
            parsed_results = []

            for crawl_result in successful_crawls:
                parsed_result = await process_business_with_gemini(crawl_result, parser)
                parsed_results.append(parsed_result)

                # Small delay between Gemini API calls to respect rate limits
                await asyncio.sleep(1)

            parsing_end_time = time.time()
            parsing_time = parsing_end_time - parsing_start_time

            # Save all results
            os.makedirs("demo_output", exist_ok=True)

            # Save individual results
            for result in parsed_results:
                business_id = result['business_id']
                business_name = result['business_info'].get('name', f'Business {business_id}')
                safe_name = "".join(c for c in business_name if c.isalnum() or c in (' ', '-', '_')).strip().replace(' ', '_')

                # Save combined content
                if 'combined_content' in result:
                    content_file = f"demo_output/{safe_name}_{business_id}_content.md"
                    with open(content_file, 'w', encoding='utf-8') as f:
                        f.write(result['combined_content'])

                # Save parsed data
                if 'parsed_data' in result:
                    parsed_file = f"demo_output/{safe_name}_{business_id}_parsed.json"
                    with open(parsed_file, 'w', encoding='utf-8') as f:
                        json.dump(result['parsed_data'], f, indent=2, ensure_ascii=False)

            # Save summary
            summary = {
                'timestamp': datetime.now().isoformat(),
                'total_businesses': len(target_businesses),
                'successful_crawls': len(successful_crawls),
                'crawl_time_seconds': crawl_time,
                'parsing_time_seconds': parsing_time,
                'total_time_seconds': parsing_end_time - start_time,
                'results': parsed_results
            }

            with open("demo_output/concurrent_demo_summary.json", 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False, default=str)

            print(f"\n📊 Final Summary:")
            print(f"   🕷️  Crawling: {crawl_time:.1f}s for {len(target_businesses)} sites")
            print(f"   🤖 Parsing: {parsing_time:.1f}s for {len(successful_crawls)} sites")
            print(f"   ⏱️  Total time: {parsing_end_time - start_time:.1f}s")
            print(f"   🚀 Concurrent speedup demonstrated!")

            # Display individual results
            print(f"\n📋 Individual Results:")
            for result in parsed_results:
                business_name = result['business_info'].get('name', 'Unknown')
                if result.get('parsed_data'):
                    services_count = len(result['parsed_data'].get('services', []))
                    phone_count = len(result['parsed_data'].get('phone_numbers', []))
                    print(f"   ✅ {business_name}: {services_count} services, {phone_count} phone numbers")
                else:
                    print(f"   ❌ {business_name}: Parsing failed")

        print(f"\n🎉 Concurrent demo completed successfully!")
        print(f"\nGenerated files in demo_output/:")
        print(f"   📄 Raw crawl data: demo_scraped_data/business_*/")
        print(f"   📝 Combined content: *_content.md files")
        print(f"   🤖 Parsed results: *_parsed.json files")
        print(f"   📊 Summary: concurrent_demo_summary.json")

    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        print("\nTroubleshooting tips:")
        print("1. Check your internet connection")
        print("2. Verify API keys in .env file")
        print("3. Try with different website URLs")
        print("4. Check if websites block automated requests")

def main():
    """Main function"""
    print("Starting concurrent demo...")
    asyncio.run(demo_concurrent_crawling())

if __name__ == "__main__":
    main()
