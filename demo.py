#!/usr/bin/env python3
"""
Demo script for the ScrapeNinja Asynchronous Web Scraper

This script demonstrates how to use the scraper with a single business
from your input file.
"""

import asyncio
import json
import os
from datetime import datetime

from async_scraper import Async<PERSON>ebCrawler, CrawlConfig
from gemini_parser import GeminiParser

async def demo_single_business():
    """Demo scraping and parsing for a single business"""
    
    print("🚀 ScrapeNinja Asynchronous Web Scraper Demo")
    print("=" * 50)
    
    # Load the first business from the input file
    input_file = "input/Wichita KS.json"
    
    if not os.path.exists(input_file):
        print(f"❌ Input file {input_file} not found")
        return
    
    with open(input_file, 'r', encoding='utf-8') as f:
        businesses = json.load(f)
    
    # Find the first business with a website
    target_business = None
    for business in businesses:
        if isinstance(business, dict) and business.get('website'):
            website_url = business['website']
            if website_url.startswith(('http://', 'https://')):
                target_business = business
                break
    
    if not target_business:
        print("❌ No businesses with websites found in the input file")
        return
    
    print(f"📋 Selected Business: {target_business.get('name', 'Unknown')}")
    print(f"🌐 Website: {target_business['website']}")
    print(f"📍 Address: {target_business.get('formatted_address', 'Unknown')}")
    print()
    
    # Configure the crawler for a quick demo
    config = CrawlConfig(
        max_pages=5,        # Limit to 5 pages for demo
        max_depth=2,        # Don't go too deep
        delay_between_requests=1.0,  # 1 second delay
        timeout=30,
        retries=2
    )
    
    print("⚙️  Crawl Configuration:")
    print(f"   Max pages: {config.max_pages}")
    print(f"   Max depth: {config.max_depth}")
    print(f"   Delay: {config.delay_between_requests}s")
    print()
    
    try:
        # Step 1: Scrape the website
        print("🕷️  Step 1: Scraping website...")
        crawler = AsyncWebCrawler(config)
        
        crawl_results = await crawler.crawl_website(
            target_business['website'], 
            "demo_scraped_data"
        )
        
        pages_scraped = len(crawl_results['scraped_content'])
        print(f"✅ Successfully scraped {pages_scraped} pages")
        
        if pages_scraped == 0:
            print("❌ No content was scraped. This might be due to:")
            print("   - Website blocking automated requests")
            print("   - Network connectivity issues")
            print("   - Invalid website URL")
            return
        
        # Step 2: Combine content
        print("\n📝 Step 2: Combining scraped content...")
        
        combined_content = []
        business_name = target_business.get('name', 'Unknown Business')
        
        combined_content.append(f"# Website Crawl Results for {business_name}")
        combined_content.append(f"# Starting URL: {target_business['website']}")
        combined_content.append(f"# Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        combined_content.append("")
        
        # Add business context
        combined_content.append("## Business Information")
        if 'formatted_address' in target_business:
            combined_content.append(f"**Address:** {target_business['formatted_address']}")
        if 'rating' in target_business:
            combined_content.append(f"**Rating:** {target_business['rating']}")
        combined_content.append("")
        
        # Add scraped content
        for i, page_data in enumerate(crawl_results['scraped_content'], 1):
            url = page_data.get('url', 'Unknown URL')
            content = page_data.get('content', '')
            
            combined_content.append("=" * 80)
            combined_content.append(f"Page {i}: {url}")
            combined_content.append("=" * 80)
            combined_content.append("")
            combined_content.append(content)
            combined_content.append("")
        
        combined_text = "\n".join(combined_content)
        
        # Save combined content
        os.makedirs("demo_output", exist_ok=True)
        combined_file = "demo_output/demo_combined_content.md"
        with open(combined_file, 'w', encoding='utf-8') as f:
            f.write(combined_text)
        
        print(f"✅ Combined content saved to {combined_file}")
        
        # Step 3: Parse with Gemini AI
        print("\n🤖 Step 3: Parsing content with Gemini AI...")
        
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if not gemini_api_key:
            print("❌ GEMINI_API_KEY not found. Skipping AI parsing.")
            print("   Add your Gemini API key to the .env file to enable parsing.")
            return
        
        parser = GeminiParser(gemini_api_key)
        parser.set_location_info(target_business)
        
        parsed_data = parser.parse_content(
            combined_text,
            save_preprocessed=True,
            preprocessed_file="demo_output/demo_preprocessed.txt",
            homepage_url=target_business['website']
        )
        
        if parsed_data:
            # Save parsed results
            parsed_file = "demo_output/demo_parsed_results.json"
            with open(parsed_file, 'w', encoding='utf-8') as f:
                json.dump(parsed_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Parsed results saved to {parsed_file}")
            
            # Display summary
            print("\n📊 Parsing Results Summary:")
            print(f"   Business Name: {parsed_data.get('name', 'Not found')}")
            print(f"   Phone Numbers: {len(parsed_data.get('phone_numbers', []))}")
            print(f"   Services: {len(parsed_data.get('services', []))}")
            print(f"   Emergency Care: {'Yes' if parsed_data.get('emergency') else 'No'}")
            
            if parsed_data.get('opening_hours'):
                print("   Opening Hours: Available")
            else:
                print("   Opening Hours: Not found")
        
        else:
            print("❌ Parsing failed. Check the logs for details.")
        
        print("\n🎉 Demo completed successfully!")
        print("\nGenerated files:")
        print(f"   📄 Raw crawl data: demo_scraped_data/")
        print(f"   📝 Combined content: {combined_file}")
        print(f"   🤖 Parsed results: demo_output/demo_parsed_results.json")
        print(f"   🔧 Preprocessed content: demo_output/demo_preprocessed.txt")
        
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        print("\nTroubleshooting tips:")
        print("1. Check your internet connection")
        print("2. Verify API keys in .env file")
        print("3. Try with a different website URL")
        print("4. Check if the website blocks automated requests")

def main():
    """Main function"""
    print("Starting demo...")
    asyncio.run(demo_single_business())

if __name__ == "__main__":
    main()
